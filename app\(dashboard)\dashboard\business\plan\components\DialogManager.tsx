"use client";

import { toast } from "sonner";
import { openRazorpaySubscriptionCheckout } from "@/lib/razorpay/utils/loadRazorpaySDK";
import { confirmSubscriptionPayment } from "@/lib/actions/subscription/confirm";
import SimplifiedPlanActionDialog from "./SimplifiedPlanActionDialog";
import FirstTimePaidPlanDialog from "./FirstTimePaidPlanDialog";
import { UpiPaymentMethodWarning } from "./UpiPaymentMethodWarning";
import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

interface DialogManagerProps {
  dialogPlan: PricingPlan | null;
  isPlanDialogOpen: boolean;
  isFirstTimePaidPlanDialogOpen: boolean;
  isUpiWarningDialogOpen: boolean;
  pendingSubscribeAction: (() => Promise<Response | undefined>) | null;
  dialogLoading: boolean;
  billingCycle: "monthly" | "yearly";
  subscriptionStatus: SubscriptionStatus;
  trialEndDate: string | null;
  lastPaymentMethod?: string | null;
  
  // Setters
  setIsPlanDialogOpen: (_open: boolean) => void;
  setIsFirstTimePaidPlanDialogOpen: (_open: boolean) => void;
  setIsUpiWarningDialogOpen: (_open: boolean) => void;
  setPendingSubscribeAction: (_action: (() => Promise<Response | undefined>) | null) => void;
  setDialogLoading: (_loading: boolean) => void;
  setActiveTab: (_tab: "plans" | "subscription") => void;

  // Functions
  handleDialogSubscribe: () => Promise<void>;
  handleActivateTrial: () => Promise<void>;
  resetProcessing: () => void;
  startProcessing: (_message: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  setSubscriptionCreated: (_message: string) => void;
  setFuturePaymentAuthorized: (_message: string) => void;
  router: AppRouterInstance;
}

export default function DialogManager({
  dialogPlan,
  isPlanDialogOpen,
  isFirstTimePaidPlanDialogOpen,
  isUpiWarningDialogOpen,
  pendingSubscribeAction,
  dialogLoading,
  billingCycle,
  subscriptionStatus,
  trialEndDate,
  lastPaymentMethod,
  setIsPlanDialogOpen,
  setIsFirstTimePaidPlanDialogOpen,
  setIsUpiWarningDialogOpen,
  setPendingSubscribeAction,
  setDialogLoading,
  setActiveTab,
  handleDialogSubscribe,
  handleActivateTrial,
  resetProcessing,
  startProcessing,
  completeProcessing,
  setSubscriptionCreated,
  setFuturePaymentAuthorized,
  router,
}: DialogManagerProps) {

  // Handle UPI warning dialog continue action
  const handleUpiWarningContinue = async () => {
    // Reset the dialog state
    setIsUpiWarningDialogOpen(false);

    // Close the plan dialog since we're proceeding with the subscription
    setIsPlanDialogOpen(false);

    // Set dialog loading state to true
    setDialogLoading(true);

    try {
      // Start processing state
      startProcessing("Processing your subscription request...");

      // Execute the pending action
      if (pendingSubscribeAction && typeof pendingSubscribeAction === 'function') {
        const actionResult = await pendingSubscribeAction();

        // If the action returned a response, process it
        if (actionResult && typeof actionResult.json === 'function') {
          const data = await actionResult.json();

          if (!data.success) {
            // Show error toast with specific error message
            const errorMessage = data.error || "Failed to create subscription. Please try again.";

            toast.error("Subscription Error", {
              description: errorMessage,
            });

            completeProcessing(false, errorMessage);
            return;
          }

          // Continue with the normal flow
          console.log("[RAZORPAY_DEBUG] Checking for subscription ID in response:", {
            hasDataId: !!data.data?.id,
            hasDataSubscriptionId: !!data.data?.subscription_id,
            dataKeys: Object.keys(data.data || {}),
            fullData: data.data
          });

          const subscriptionId = data.data?.id || data.data?.subscription_id;

          if (subscriptionId) {
            // Log the subscription ID for debugging
            console.log("[RAZORPAY_DEBUG] Got subscription ID:", subscriptionId);
            console.log("[RAZORPAY_DEBUG] Full data:", data.data);

            try {
              // Update processing message
              startProcessing("Opening payment authorization...");

              // Debug logging for subscription response
              console.log("[RAZORPAY_DEBUG] Subscription response:", {
                subscription_id: subscriptionId,
                requires_authorization: data.data.requires_authorization,
                status: data.data.status,
                current_subscription_status: subscriptionStatus
              });

              console.log("[RAZORPAY_DEBUG] Always using Razorpay checkout modal for consistent UX:", subscriptionId);

              // For regular subscriptions, use the normal Razorpay checkout flow
              // Fetch Razorpay key ID from the server
              const keyResponse = await fetch("/api/razorpay/key");

              if (!keyResponse.ok) {
                throw new Error("Failed to fetch Razorpay key ID");
              }

              const keyData = await keyResponse.json();

              if (!keyData.success || !keyData.key_id) {
                throw new Error("Invalid Razorpay key ID response");
              }

              const keyId = keyData.key_id;

              // Open Razorpay checkout for payment authorization
              const response = await openRazorpaySubscriptionCheckout(
                keyId,
                subscriptionId,
                {
                  name: "Dukancard",
                  description: `${dialogPlan?.name} Plan (${billingCycle})`,
                  theme: {
                    color: "#6366f1" // Primary color
                  },
                  prefill: {
                    name: "", // Will be filled by Razorpay from the subscription
                    email: "", // Will be filled by Razorpay from the subscription
                    contact: "" // Will be filled by Razorpay from the subscription
                  },
                  notes: {} // Empty notes object - Razorpay will use the notes from the subscription
                }
              );

              // Payment authorized successfully
              if (response.razorpay_payment_id) {
                // Update processing message
                startProcessing("Confirming payment...");

                // Confirm the payment with the server
                const confirmResult = await confirmSubscriptionPayment(
                  subscriptionId,
                  response.razorpay_payment_id
                );

                if (!confirmResult.success) {
                  throw new Error(confirmResult.error || "Failed to confirm payment");
                }

                // Check if this is a future payment (trial user)
                const isFuturePayment = confirmResult.data?.is_future_payment === true;

                // Show appropriate success toast based on payment timing
                if (isFuturePayment) {
                  // For trial users with future payment
                  setFuturePaymentAuthorized("Your subscription has been authorized. Payment will be processed when your trial ends.");
                } else {
                  // For immediate payments
                  toast.success("Payment Authorized", {
                    description: `Your ${dialogPlan?.name} plan subscription has been authorized successfully.`,
                  });
                }

                // Set subscription created status
                setSubscriptionCreated(`Your ${dialogPlan?.name} plan subscription has been created and authorized successfully.`);

                // Set active tab to subscription
                setActiveTab("subscription");

                // Reset dialog loading state
                setDialogLoading(false);

                // Refresh the page to get the latest subscription status
                setTimeout(() => {
                  router.refresh();
                }, 1500);
              }
            } catch (paymentError) {
              // Handle payment errors
              if (paymentError && typeof paymentError === 'object' && 'cancelled' in paymentError) {
                // Just log a normal message instead of an error for user cancellation
                console.log("User cancelled the payment process");

                // Dismiss all toast notifications using Sonner's dismiss method
                toast.dismiss();

                // Reset processing state immediately
                resetProcessing();

                // Reset dialog loading state
                setDialogLoading(false);
              } else {
                // Log actual errors to console
                console.error("Payment authorization error:", paymentError);

                // Show error toast for other errors
                toast.error("Payment Authorization Failed", {
                  description: paymentError instanceof Error ? paymentError.message : "Failed to authorize payment. Please try again.",
                });

                // Complete processing with error
                completeProcessing(false, "Payment authorization failed. Please try again.");

                // Reset dialog loading state
                setDialogLoading(false);
              }
            }
          } else {
            // No subscription ID found in response
            console.error("[RAZORPAY_DEBUG] No subscription ID found in response:", data);
            throw new Error("No subscription ID returned from server");
          }
        }
      } else {
        // If pendingSubscribeAction is not a function, show error
        throw new Error("Invalid subscription action. Please try again.");
      }
    } catch (error) {
      console.error("Error executing pending action:", error);

      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred. Please try again.";

      // Show error toast
      toast.error("Subscription Error", {
        description: errorMessage,
      });

      // Reset processing state
      completeProcessing(false, errorMessage);

      // Reset dialog loading state
      setDialogLoading(false);
    } finally {
      // Clear the pending action
      setPendingSubscribeAction(null);
    }
  };

  // Handle UPI warning dialog cancel action
  const handleUpiWarningCancel = () => {
    // Reset the dialog state
    setIsUpiWarningDialogOpen(false);

    // Clear the pending action
    setPendingSubscribeAction(null);

    // Reset dialog loading state
    setDialogLoading(false);
  };

  return (
    <>
      {/* Plan Action Dialog */}
      {dialogPlan && (
        <SimplifiedPlanActionDialog
          isOpen={isPlanDialogOpen}
          onClose={() => {
            setIsPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          trialEndDate={
            subscriptionStatus === "authenticated" ? null : trialEndDate
          }
          _onSubscribe={handleDialogSubscribe}
          isLoading={dialogLoading} // Pass loading state to dialog
        />
      )}

      {/* First Time Paid Plan Dialog */}
      {dialogPlan && (
        <FirstTimePaidPlanDialog
          isOpen={isFirstTimePaidPlanDialogOpen}
          onClose={() => {
            setIsFirstTimePaidPlanDialogOpen(false);
            setDialogLoading(false); // Reset loading state when dialog is closed
            resetProcessing(); // Also reset the processing state to ensure no lingering toast notifications
          }}
          plan={dialogPlan}
          billingCycle={billingCycle} // Pass the selected billing cycle
          onActivateTrial={handleActivateTrial}
          isLoading={dialogLoading} // Pass loading state to dialog
        />
      )}

      {/* UPI Payment Method Warning Dialog */}
      {isUpiWarningDialogOpen && pendingSubscribeAction && typeof pendingSubscribeAction === 'function' && (
        <UpiPaymentMethodWarning
          paymentMethod={lastPaymentMethod || null}
          onContinue={handleUpiWarningContinue}
          onCancel={handleUpiWarningCancel}
        />
      )}
    </>
  );
}
