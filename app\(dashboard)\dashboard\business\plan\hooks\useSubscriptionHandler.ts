"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useRef } from "react";

import { PricingPlan } from "@/lib/PricingPlans";
import { SubscriptionStatus } from "../page";


interface UseSubscriptionHandlerProps {
  currentSubscriptionId: string | null;
  subscriptionStatus: SubscriptionStatus;
  currentPlanDetails?: PricingPlan;
  currentPlanCycle: "monthly" | "yearly";
  lastPaymentMethod?: string | null;
  razorpaySubscriptionId?: string | null;
  trialEndDate?: string | null;

  // From subscription logic hook
  dialogPlan: PricingPlan | null;
  billingCycle: "monthly" | "yearly";
  setDialogLoading: (_loading: boolean) => void;
  setIsPlanDialogOpen: (_open: boolean) => void;
  setIsUpiWarningDialogOpen: (_open: boolean) => void;
  setPendingSubscribeAction: (_action: (() => Promise<Response | undefined>) | null) => void;

  // Processing functions
  startProcessing: (_message: string) => void;
  completeProcessing: (_success: boolean, _message?: string) => void;
  resetProcessing: () => void;
  setSubscriptionCreated: (_message: string) => void;
  setFuturePaymentAuthorized: (_message: string) => void;

  // Validation and helper functions
  validateSubscriptionRequest: (_plan: PricingPlan) => boolean;
  handleFreePlanSubscription: (_plan: PricingPlan) => Promise<boolean>;
  determineSubscriptionFlow: () => {
    hasRazorpaySubscription: boolean;
    isCardPayment: boolean;
    isUpiOrEmandateOrUnknown: boolean;
    paymentMethod: string;
  };

  // Additional required functions
  setActiveTab: (_tab: "plans" | "subscription") => void;
}

export function useSubscriptionHandler(props: UseSubscriptionHandlerProps) {
  const router = useRouter();

  // Add ref to track if a subscription request is in progress
  const isRequestInProgress = useRef(false);

  const {
    currentSubscriptionId,
    subscriptionStatus,
    currentPlanDetails: _currentPlanDetails,
    currentPlanCycle: _currentPlanCycle,
    lastPaymentMethod,
    razorpaySubscriptionId,
    trialEndDate,
    dialogPlan,
    billingCycle,
    setDialogLoading,
    setIsPlanDialogOpen,
    setIsUpiWarningDialogOpen,
    setPendingSubscribeAction,
    startProcessing,
    completeProcessing,
    resetProcessing,
    setSubscriptionCreated,
    setFuturePaymentAuthorized,
    validateSubscriptionRequest,
    handleFreePlanSubscription,
    determineSubscriptionFlow,
    setActiveTab,
  } = props;





  // Main subscription handler - now using centralized logic with payment method check
  const handleDialogSubscribe = async () => {
    // Prevent duplicate requests
    if (isRequestInProgress.current) {
      console.log('[SUBSCRIPTION_HANDLER] Request already in progress, ignoring duplicate call');
      return;
    }

    console.log(`[CENTRALIZED_SUBSCRIPTION] handleDialogSubscribe called`);
    console.log(`[CENTRALIZED_SUBSCRIPTION] Current state:`, {
      dialogPlan: dialogPlan?.id,
      currentSubscriptionId,
      subscriptionStatus,
      lastPaymentMethod,
      razorpaySubscriptionId,
    });

    if (!dialogPlan) return;

    // Mark request as in progress
    isRequestInProgress.current = true;

    // Set dialog loading state to true
    setDialogLoading(true);

    try {
      // Check if this is a free plan - handle separately for now
      if (dialogPlan.id === "free") {
        const success = await handleFreePlanSubscription(dialogPlan);
        if (success) return;
      }

      // Validate subscription request
      if (!validateSubscriptionRequest(dialogPlan)) {
        setDialogLoading(false);
        return;
      }

      // Create a subscription action that returns a Response object for DialogManager
      // Add unique request ID to help with debugging and prevent duplicates
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      console.log('[SUBSCRIPTION_HANDLER] Creating subscription with request ID:', requestId);

      const subscriptionAction = async () => {
        const response = await fetch('/api/subscriptions/centralized', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': requestId // Add request ID for debugging
          },
          body: JSON.stringify({
            planId: dialogPlan.id,
            planCycle: billingCycle,
          }),
        });
        return response;
      };

      // Determine if we need to show UPI warning or execute immediately
      const hasExistingSubscription = !!currentSubscriptionId && (subscriptionStatus === "authenticated" || subscriptionStatus === "active");

      // Check if user is currently on trial using proper logic
      // A user is on trial if they have a trial end date that's in the future
      const isOnTrial = trialEndDate ? new Date(trialEndDate) > new Date() : false;

      console.log('[SUBSCRIPTION_HANDLER] Trial detection:', {
        trialEndDate,
        isOnTrial,
        subscriptionStatus,
        hasExistingSubscription
      });

      // For active subscriptions, check payment method from Razorpay API
      let shouldShowUpiWarning = false;

      if (hasExistingSubscription && !isOnTrial && subscriptionStatus === 'active') {
        console.log('[SUBSCRIPTION_HANDLER] Checking payment method for active subscription');

        try {
          const response = await fetch('/api/subscriptions/check-payment-method');
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
              shouldShowUpiWarning = result.data.requiresNewSubscription;

              console.log('[SUBSCRIPTION_HANDLER] Payment method check result:', {
                paymentMethod: result.data.paymentMethod,
                requiresNewSubscription: result.data.requiresNewSubscription,
                warningMessage: result.data.warningMessage
              });
            }
          }
        } catch (error) {
          console.error('[SUBSCRIPTION_HANDLER] Error checking payment method:', error);
          // Continue with default behavior if API fails
        }
      } else if (hasExistingSubscription && !isOnTrial) {
        // For authenticated subscriptions, use the old logic as fallback
        const { isUpiOrEmandateOrUnknown } = determineSubscriptionFlow();
        shouldShowUpiWarning = isUpiOrEmandateOrUnknown;
      }

      if (!shouldShowUpiWarning) {
        console.log('[SUBSCRIPTION_HANDLER] Executing subscription action immediately', {
          isOnTrial,
          hasExistingSubscription,
          shouldShowUpiWarning
        });

        // Execute the action immediately
        try {
          startProcessing("Processing your subscription request...");

          console.log('[SUBSCRIPTION_HANDLER] Making API call to create subscription');
          const actionResult = await subscriptionAction();
          console.log('[SUBSCRIPTION_HANDLER] API call completed, parsing response');
          const data = await actionResult.json();
          console.log('[SUBSCRIPTION_HANDLER] Response data:', data);

          if (!data.success) {
            const errorMessage = data.error || "Failed to create subscription. Please try again.";
            toast.error("Subscription Error", { description: errorMessage });
            completeProcessing(false, errorMessage);
            return;
          }

          // Get subscription ID from response
          const subscriptionId = data.data?.id || data.data?.subscription_id;

          if (subscriptionId) {
            console.log("[SUBSCRIPTION_HANDLER] Got subscription ID:", subscriptionId);

            // Update processing message
            startProcessing("Opening payment authorization...");

            // Fetch Razorpay key ID
            const keyResponse = await fetch("/api/razorpay/key");
            if (!keyResponse.ok) {
              throw new Error("Failed to fetch Razorpay key ID");
            }

            const keyData = await keyResponse.json();
            if (!keyData.success || !keyData.key_id) {
              throw new Error("Invalid Razorpay key ID response");
            }

            // Import the Razorpay function
            const { openRazorpaySubscriptionCheckout } = await import("@/lib/razorpay/utils/loadRazorpaySDK");

            // Open Razorpay checkout
            const response = await openRazorpaySubscriptionCheckout(
              keyData.key_id,
              subscriptionId,
              {
                name: "Dukancard",
                description: `${dialogPlan.name} Plan (${billingCycle})`,
                theme: { color: "#6366f1" },
                prefill: { name: "", email: "", contact: "" },
                notes: {}
              }
            );

            // Handle payment success
            if (response.razorpay_payment_id) {
              startProcessing("Confirming payment...");

              // Import and call payment confirmation
              const { confirmSubscriptionPayment } = await import("@/lib/actions/subscription/confirm");
              const confirmResult = await confirmSubscriptionPayment(
                subscriptionId,
                response.razorpay_payment_id
              );

              if (!confirmResult.success) {
                throw new Error(confirmResult.error || "Failed to confirm payment");
              }

              // Show success message
              const isFuturePayment = confirmResult.data?.is_future_payment === true;
              if (isFuturePayment) {
                setFuturePaymentAuthorized("Your subscription has been authorized. Payment will be processed when your trial ends.");
              } else {
                toast.success("Payment Authorized", {
                  description: `Your ${dialogPlan.name} plan subscription has been authorized successfully.`,
                });
              }

              setSubscriptionCreated(`Your ${dialogPlan.name} plan subscription has been created and authorized successfully.`);
              setActiveTab("subscription");
              setIsPlanDialogOpen(false);

              // Refresh the page
              setTimeout(() => {
                router.refresh();
              }, 1500);
            }
          } else {
            throw new Error("No subscription ID returned from server");
          }
        } catch (paymentError) {
          console.error("Payment error:", paymentError);

          if (paymentError && typeof paymentError === 'object' && 'cancelled' in paymentError) {
            console.log("User cancelled the payment process");
            toast.dismiss();
            resetProcessing();
          } else {
            toast.error("Payment Authorization Failed", {
              description: paymentError instanceof Error ? paymentError.message : "Failed to authorize payment. Please try again.",
            });
            completeProcessing(false, "Payment authorization failed. Please try again.");
          }
        }
      } else {
        console.log('[SUBSCRIPTION_HANDLER] Showing UPI warning dialog for post-trial subscription with UPI/eMandate', {
          isOnTrial,
          hasExistingSubscription,
          shouldShowUpiWarning
        });

        // Set the pending action for DialogManager to handle via UPI warning
        // Use a wrapper function to ensure the same request ID is used
        const wrappedSubscriptionAction = async () => {
          // Check if there's already an ongoing request
          if (isRequestInProgress.current) {
            console.log('[SUBSCRIPTION_HANDLER] Request already in progress, ignoring duplicate in UPI flow');
            throw new Error('Request already in progress');
          }

          // Mark request as in progress
          isRequestInProgress.current = true;

          try {
            return await subscriptionAction();
          } finally {
            // Reset the flag after the request completes
            isRequestInProgress.current = false;
          }
        };

        setPendingSubscribeAction(wrappedSubscriptionAction);

        // Show UPI warning dialog
        setIsUpiWarningDialogOpen(true);
        // Keep the plan dialog open until the UPI warning is handled
        // setIsPlanDialogOpen(false);
      }

    } catch (error) {
      console.error("Error in subscription handler:", error);

      const errorMessage = error instanceof Error
        ? error.message
        : "An unexpected error occurred. Please try again.";

      // Show error toast
      toast.error("Subscription Error", {
        description: errorMessage,
      });

      // Reset processing state
      completeProcessing(false, errorMessage);
    } finally {
      // Reset dialog loading state
      setDialogLoading(false);

      // Reset request in progress flag
      isRequestInProgress.current = false;
    }
  };

  return {
    handleDialogSubscribe,
  };
}
